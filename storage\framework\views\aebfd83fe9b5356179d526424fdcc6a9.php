<?php if (isset($component)) { $__componentOriginal4969f54a92451522b65593c595a4fb0c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4969f54a92451522b65593c595a4fb0c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.unilink-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">All Organizations</h1>
                <p class="text-gray-600 mt-1">Discover and join student organizations on campus</p>
            </div>
            <?php if(auth()->user()->hasManagementAccess()): ?>
                <a href="<?php echo e(route('organizations.create')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Create Organization
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Search and Filter Bar -->
    <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Search -->
            <div class="flex-1">
                <div class="relative">
                    <input type="text" placeholder="Search organizations..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- Category Filter -->
            <div class="md:w-48">
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option>All Categories</option>
                    <option>Academic</option>
                    <option>Sports</option>
                    <option>Arts & Culture</option>
                    <option>Community Service</option>
                    <option>Professional</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Organizations Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php $__empty_1 = true; $__currentLoopData = $organizations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $organization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <!-- Cover Image -->
                <div class="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                    <?php if($organization->cover_image): ?>
                        <img src="<?php echo e(asset('storage/' . $organization->cover_image)); ?>" alt="<?php echo e($organization->name); ?>" class="w-full h-full object-cover">
                    <?php endif; ?>
                    
                    <!-- Logo -->
                    <div class="absolute -bottom-6 left-4">
                        <div class="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center">
                            <?php if($organization->logo): ?>
                                <img src="<?php echo e(asset('storage/' . $organization->logo)); ?>" alt="<?php echo e($organization->name); ?>" class="w-10 h-10 rounded-lg object-cover">
                            <?php else: ?>
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-blue-600 font-semibold text-sm"><?php echo e(substr($organization->name, 0, 2)); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-4 pt-8">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg font-semibold text-gray-900 truncate"><?php echo e($organization->name); ?></h3>
                        <span class="text-sm text-gray-500"><?php echo e($organization->active_members_count); ?> members</span>
                    </div>
                    
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e(Str::limit($organization->description, 100)); ?></p>
                    
                    <!-- Organization Info -->
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Created by <?php echo e($organization->creator->name); ?>

                    </div>

                    <!-- Actions -->
                    <div class="flex gap-2">
                        <a href="<?php echo e(route('organizations.show', $organization)); ?>" class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            View Details
                        </a>
                        
                        <?php if(auth()->guard()->check()): ?>
                            <?php
                                $userMembership = $organization->members()->where('user_id', auth()->id())->first();
                            ?>
                            
                            <?php if(!$userMembership): ?>
                                <form action="<?php echo e(route('organizations.join', $organization)); ?>" method="POST" class="flex-1">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                        Join
                                    </button>
                                </form>
                            <?php elseif($userMembership->pivot->status === 'pending'): ?>
                                <button disabled class="flex-1 bg-yellow-100 text-yellow-800 py-2 px-4 rounded-lg text-sm font-medium cursor-not-allowed">
                                    Pending
                                </button>
                            <?php else: ?>
                                <button disabled class="flex-1 bg-gray-100 text-gray-600 py-2 px-4 rounded-lg text-sm font-medium cursor-not-allowed">
                                    Member
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No organizations found</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating the first organization.</p>
                <?php if(auth()->user()->hasManagementAccess()): ?>
                    <div class="mt-6">
                        <a href="<?php echo e(route('organizations.create')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Create Organization
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if($organizations->hasPages()): ?>
        <div class="mt-8">
            <?php echo e($organizations->links()); ?>

        </div>
    <?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $attributes = $__attributesOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__attributesOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $component = $__componentOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__componentOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/organizations/index.blade.php ENDPATH**/ ?>
@props(['post'])

<div class="post-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
     data-type="{{ $post->type }}"
     data-organization="{{ $post->organization_id ?? '' }}"
     data-has-images="{{ $post->images && count($post->images) > 0 ? 'true' : 'false' }}"
     data-is-pinned="{{ $post->is_pinned ? 'true' : 'false' }}"
     data-post-id="{{ $post->id }}">
    <!-- Post Header -->
    <div class="p-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            @if($post->organization)
                <a href="{{ route('organizations.show', $post->organization) }}">
                    <img class="h-12 w-12 rounded-full hover:opacity-80 transition-opacity"
                         src="{{ $post->organization->logo ? asset('storage/' . $post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($post->organization->name) . '&color=3B82F6&background=DBEAFE' }}"
                         alt="{{ $post->organization->name }}">
                </a>
            @else
                <a href="{{ route('profile.user', $post->user) }}">
                    <img class="h-12 w-12 rounded-full hover:opacity-80 transition-opacity"
                         src="{{ $post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                         alt="{{ $post->user->name }}">
                </a>
            @endif
            <div class="flex-1">
                <div class="flex items-center space-x-2">
                    @if($post->organization)
                        <a href="{{ route('organizations.show', $post->organization) }}" class="text-lg font-semibold text-gray-900 hover:text-custom-green">
                            {{ $post->organization->name }}
                        </a>
                    @else
                        <a href="{{ route('profile.user', $post->user) }}" class="text-lg font-semibold text-gray-900 hover:text-custom-green">
                            {{ $post->user->name }}
                        </a>
                    @endif
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        @if($post->type === 'event') bg-blue-100 text-blue-800
                        @elseif($post->type === 'announcement') bg-yellow-100 text-yellow-800
                        @elseif($post->type === 'financial_report') bg-green-100 text-green-800
                        @else bg-gray-100 text-gray-800
                        @endif">
                        {{ ucfirst(str_replace('_', ' ', $post->type)) }}
                    </span>
                </div>
                <p class="text-sm text-gray-500">
                    @if($post->organization)
                        by <x-user-link :user="$post->user" size="xs" :show-avatar="false" class="inline text-sm text-gray-500 hover:text-custom-green" /> •
                    @endif
                    {{ $post->published_at->diffForHumans() }}
                </p>
            </div>
            
            <!-- Post Actions Dropdown -->
            @if(auth()->id() === $post->user_id || auth()->user()->isAdmin())
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                        </svg>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border">
                        <a href="{{ route('posts.edit', $post) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                        <form action="{{ route('posts.destroy', $post) }}" method="POST" class="block">
                            @csrf
                            @method('DELETE')
                            <button type="submit" onclick="return confirm('Are you sure you want to delete this post?')" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</button>
                        </form>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Post Content -->
    <div class="p-4">
        <h4 class="text-xl font-semibold text-gray-900 mb-2">{{ $post->title }}</h4>
        <div class="text-gray-700 mb-4">
            @if(strlen($post->content) > 300)
                <div x-data="{ expanded: false }">
                    <div x-show="!expanded">
                        {{ Str::limit($post->content, 300) }}
                        <button @click="expanded = true" class="text-blue-600 hover:text-blue-800 font-medium">Read more</button>
                    </div>
                    <div x-show="expanded">
                        {!! nl2br(e($post->content)) !!}
                        <button @click="expanded = false" class="text-blue-600 hover:text-blue-800 font-medium">Show less</button>
                    </div>
                </div>
            @else
                {!! nl2br(e($post->content)) !!}
            @endif
        </div>

        <!-- Images -->
        @if($post->images && count($post->images) > 0)
            <div class="mb-4">
                @if(count($post->images) === 1)
                    <img src="{{ asset('storage/' . $post->images[0]) }}"
                         alt="Post image"
                         class="w-full max-h-96 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                         onclick="openImageModal({{ json_encode($post->images) }}, 0)">
                @else
                    <div class="grid grid-cols-2 gap-2">
                        @foreach($post->images as $index => $image)
                            @if($index < 4)
                                <div class="relative">
                                    <img src="{{ asset('storage/' . $image) }}"
                                         alt="Post image"
                                         class="w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                                         onclick="openImageModal({{ json_encode($post->images) }}, {{ $index }})">
                                    @if($index === 3 && count($post->images) > 4)
                                        <div class="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center cursor-pointer"
                                             onclick="openImageModal({{ json_encode($post->images) }}, {{ $index }})">
                                            <span class="text-white text-lg font-semibold">+{{ count($post->images) - 4 }}</span>
                                        </div>
                                    @endif
                                </div>
                            @endif
                        @endforeach
                    </div>
                @endif
            </div>
        @endif

        <!-- Facebook Embed -->
        @if($post->facebook_embed_url)
            <div class="mb-4">
                <iframe src="{{ $post->facebook_embed_url }}" width="100%" height="315" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowfullscreen="true"></iframe>
            </div>
        @endif

        <!-- Event/Special Content -->
        @if($post->type === 'event')
            <div class="bg-blue-50 rounded-lg p-3 mb-4">
                <div class="text-sm text-blue-800">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                        </svg>
                        Event Details
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Post Actions -->
    <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <button onclick="toggleLike({{ $post->id }})" 
                        class="flex items-center space-x-2 text-gray-500 hover:text-red-600 transition-colors"
                        id="like-btn-{{ $post->id }}">
                    <svg class="w-5 h-5 {{ $post->isLikedBy(auth()->user()) ? 'text-red-600 fill-current' : '' }}" 
                         fill="{{ $post->isLikedBy(auth()->user()) ? 'currentColor' : 'none' }}" 
                         stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    <span class="text-sm" id="like-count-{{ $post->id }}">{{ $post->likes->count() }} likes</span>
                </button>
                <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="text-sm">{{ $post->comments->count() }} comments</span>
                </button>
                <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    <span class="text-sm">Share</span>
                </button>
            </div>
            @if($post->type === 'event')
                <a href="{{ route('posts.show', $post) }}" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                    View Details
                </a>
            @endif
        </div>
    </div>
</div>

<script>
async function toggleLike(postId) {
    try {
        const response = await fetch(`/posts/${postId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();
        
        if (data.success) {
            const likeBtn = document.getElementById(`like-btn-${postId}`);
            const likeCount = document.getElementById(`like-count-${postId}`);
            const heartIcon = likeBtn.querySelector('svg');
            
            if (data.liked) {
                heartIcon.classList.add('text-red-600', 'fill-current');
                heartIcon.setAttribute('fill', 'currentColor');
            } else {
                heartIcon.classList.remove('text-red-600', 'fill-current');
                heartIcon.setAttribute('fill', 'none');
            }
            
            likeCount.textContent = `${data.likes_count} likes`;
        }
    } catch (error) {
        console.error('Error toggling like:', error);
    }
}
</script>

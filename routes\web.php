<?php

use App\Http\Controllers\CommentController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ScholarshipController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('dashboard');
});

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', function (Request $request) {
        $query = \App\Models\Post::with(['user', 'organization', 'comments', 'likes'])
            ->published();

        // Apply filters if provided
        if ($request->has('type') && $request->type !== 'all') {
            $query->where('type', $request->type);
        }

        if ($request->has('organization_filter')) {
            if ($request->organization_filter === 'personal') {
                $query->whereNull('organization_id');
            } elseif ($request->organization_filter === 'organizations') {
                $query->whereNotNull('organization_id');
            }
        }

        if ($request->has('with_images') && $request->with_images === 'true') {
            $query->whereNotNull('images')->where('images', '!=', '[]');
        }

        if ($request->has('pinned') && $request->pinned === 'true') {
            $query->where('is_pinned', true);
        }

        $posts = $query->latest('published_at')->paginate(10);

        // Return JSON for AJAX requests
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'posts' => $posts->items(),
                'pagination' => [
                    'current_page' => $posts->currentPage(),
                    'last_page' => $posts->lastPage(),
                    'total' => $posts->total(),
                    'has_more' => $posts->hasMorePages()
                ]
            ]);
        }

        return view('dashboard', compact('posts'));
    })->name('dashboard');

    Route::get('/announcements', function () {
        return view('announcements.index');
    })->name('announcements');

    // Post routes
    Route::get('/posts', [PostController::class, 'index'])->name('posts.index');
    Route::get('/posts/create', [PostController::class, 'create'])->name('posts.create');
    Route::post('/posts', [PostController::class, 'store'])->name('posts.store');
    Route::get('/posts/{post}', [PostController::class, 'show'])->name('posts.show');
    Route::get('/posts/{post}/edit', [PostController::class, 'edit'])->name('posts.edit');
    Route::put('/posts/{post}', [PostController::class, 'update'])->name('posts.update');
    Route::delete('/posts/{post}', [PostController::class, 'destroy'])->name('posts.destroy');
    Route::post('/posts/{post}/like', [PostController::class, 'toggleLike'])->name('posts.like');
    Route::get('/posts-filter', [PostController::class, 'filter'])->name('posts.filter');

    // Comment routes
    Route::post('/posts/{post}/comments', [CommentController::class, 'store'])->name('posts.comments.store');
    Route::put('/comments/{comment}', [CommentController::class, 'update'])->name('comments.update');
    Route::delete('/comments/{comment}', [CommentController::class, 'destroy'])->name('comments.destroy');
    Route::post('/comments/{comment}/like', [CommentController::class, 'toggleLike'])->name('comments.like');

    // Organization routes
    Route::get('/organizations', [OrganizationController::class, 'index'])->name('organizations.index');
    Route::get('/organizations/my', [OrganizationController::class, 'my'])->name('organizations.my');
    Route::get('/organizations/create', [OrganizationController::class, 'create'])->name('organizations.create')->middleware('role:admin,org_officer');
    Route::post('/organizations', [OrganizationController::class, 'store'])->name('organizations.store')->middleware('role:admin,org_officer');
    Route::get('/organizations/{organization}', [OrganizationController::class, 'show'])->name('organizations.show');
    Route::get('/organizations/{organization}/edit', [OrganizationController::class, 'edit'])->name('organizations.edit');
    Route::put('/organizations/{organization}', [OrganizationController::class, 'update'])->name('organizations.update');
    Route::delete('/organizations/{organization}', [OrganizationController::class, 'destroy'])->name('organizations.destroy');
    Route::post('/organizations/{organization}/join', [OrganizationController::class, 'join'])->name('organizations.join');
    Route::delete('/organizations/{organization}/leave', [OrganizationController::class, 'leave'])->name('organizations.leave');

    // Scholarship routes
    Route::get('/scholarships', [ScholarshipController::class, 'index'])->name('scholarships.index');
    Route::get('/scholarships/create', [ScholarshipController::class, 'create'])->name('scholarships.create')->middleware('role:admin,org_officer');
    Route::post('/scholarships', [ScholarshipController::class, 'store'])->name('scholarships.store')->middleware('role:admin,org_officer');
    Route::get('/scholarships/{scholarship}', [ScholarshipController::class, 'show'])->name('scholarships.show');
    Route::get('/scholarships/{scholarship}/edit', [ScholarshipController::class, 'edit'])->name('scholarships.edit');
    Route::put('/scholarships/{scholarship}', [ScholarshipController::class, 'update'])->name('scholarships.update');
    Route::delete('/scholarships/{scholarship}', [ScholarshipController::class, 'destroy'])->name('scholarships.destroy');

    Route::get('/admin/dashboard', function () {
        return view('admin.dashboard');
    })->name('admin.dashboard')->middleware('role:admin,org_officer');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/{user}', [ProfileController::class, 'show'])->name('profile.user');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Debug route for avatar testing
Route::get('/debug-avatar', function () {
    $user = \App\Models\User::whereNotNull('avatar')->first();
    if ($user) {
        return response()->json([
            'user_name' => $user->name,
            'avatar_field' => $user->avatar,
            'asset_url' => asset('storage/' . $user->avatar),
            'full_url' => url('storage/' . $user->avatar),
        ]);
    }
    return response()->json(['error' => 'No user with avatar found']);
});

// Debug HTML route for avatar testing
Route::get('/debug-avatar-html', function () {
    $user = \App\Models\User::whereNotNull('avatar')->first();
    if ($user) {
        $avatarUrl = asset('storage/' . $user->avatar);
        return "
        <html>
        <head><title>Avatar Debug</title></head>
        <body>
            <h1>Avatar Debug</h1>
            <p>User: {$user->name}</p>
            <p>Avatar field: {$user->avatar}</p>
            <p>Asset URL: {$avatarUrl}</p>
            <img src='{$avatarUrl}' alt='{$user->name}' style='width: 100px; height: 100px; border-radius: 50%;'>
            <br><br>
            <p>Direct storage URL test:</p>
            <img src='/storage/{$user->avatar}' alt='{$user->name}' style='width: 100px; height: 100px; border-radius: 50%;'>
        </body>
        </html>";
    }
    return 'No user with avatar found';
});

require __DIR__.'/auth.php';

<x-unilink-layout>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('posts.show', $post) }}" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Post</h1>
                <p class="text-gray-600 mt-1">Update your post content</p>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="max-w-3xl">
        <form action="{{ route('posts.update', $post) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf
            @method('PUT')
            
            <!-- User Info -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <img class="h-12 w-12 rounded-full" src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
                    <div>
                        <p class="font-medium text-gray-900">{{ auth()->user()->name }}</p>
                        <select name="organization_id" class="text-sm text-gray-600 border-0 bg-transparent focus:ring-0 p-0">
                            <option value="">Personal Post</option>
                            @foreach($organizations as $org)
                                <option value="{{ $org->id }}" {{ $post->organization_id == $org->id ? 'selected' : '' }}>{{ $org->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Post Type -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Post Type</label>
                    <select name="type" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" required>
                        <option value="general" {{ $post->type == 'general' ? 'selected' : '' }}>General</option>
                        <option value="announcement" {{ $post->type == 'announcement' ? 'selected' : '' }}>Announcement</option>
                        <option value="event" {{ $post->type == 'event' ? 'selected' : '' }}>Event</option>
                        <option value="financial_report" {{ $post->type == 'financial_report' ? 'selected' : '' }}>Financial Report</option>
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Title -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <input type="text" name="title" value="{{ old('title', $post->title) }}" placeholder="What's the title of your post?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green text-lg" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Content -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                    <textarea name="content" rows="6" placeholder="What's on your mind?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" required>{{ old('content', $post->content) }}</textarea>
                    @error('content')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Existing Images -->
                @if($post->images && count($post->images) > 0)
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Images</label>
                        <div class="grid grid-cols-2 gap-2">
                            @foreach($post->images as $index => $image)
                                <div class="relative">
                                    <img src="{{ asset('storage/' . $image) }}"
                                         class="w-full h-24 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                                         onclick="openImageModal({{ json_encode($post->images) }}, {{ $index }})">
                                    <label class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 cursor-pointer z-10">
                                        <input type="checkbox" name="remove_images[]" value="{{ $image }}" class="hidden" onchange="toggleImageRemoval(this)">
                                        ×
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Click images to view full size, or click × to mark for removal</p>
                    </div>
                @endif

                <!-- New Image Upload -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Add New Images (Optional)</label>
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center space-x-2 cursor-pointer text-custom-green hover:text-custom-second-darkest">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                            </svg>
                            <span class="text-sm font-medium">Add Photos</span>
                            <input type="file" name="images[]" multiple accept="image/*" class="hidden" onchange="previewImages(this)">
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">You can upload up to 5 images. Max 2MB each.</p>
                    
                    <!-- New Image Preview -->
                    <div id="image-preview" class="mt-3 grid grid-cols-2 gap-2 hidden"></div>
                    
                    @error('images')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    @error('images.*')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Facebook Embed URL -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Facebook Embed URL (Optional)</label>
                    <input type="url" name="facebook_embed_url" value="{{ old('facebook_embed_url', $post->facebook_embed_url) }}" placeholder="https://www.facebook.com/..." class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                    @error('facebook_embed_url')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Post Options -->
                <div class="mb-6">
                    <div class="flex items-center space-x-6">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_pinned" value="1" {{ $post->is_pinned ? 'checked' : '' }} class="rounded border-gray-300 text-custom-green shadow-sm focus:border-custom-green focus:ring focus:ring-custom-green focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Pin this post</span>
                        </label>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" required>
                        <option value="published" {{ $post->status == 'published' ? 'selected' : '' }}>Published</option>
                        <option value="draft" {{ $post->status == 'draft' ? 'selected' : '' }}>Draft</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('posts.show', $post) }}" class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-custom-green hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                    Update Post
                </button>
            </div>
        </form>
    </div>

    <script>
    function toggleImageRemoval(checkbox) {
        const container = checkbox.closest('.relative');
        const img = container.querySelector('img');
        
        if (checkbox.checked) {
            img.style.opacity = '0.5';
            container.style.border = '2px solid #ef4444';
        } else {
            img.style.opacity = '1';
            container.style.border = 'none';
        }
    }

    function previewImages(input) {
        const preview = document.getElementById('image-preview');
        preview.innerHTML = '';
        
        if (input.files && input.files.length > 0) {
            preview.classList.remove('hidden');
            
            Array.from(input.files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'relative';
                        div.innerHTML = `
                            <img src="${e.target.result}" class="w-full h-24 object-cover rounded-lg">
                            <button type="button" onclick="removeNewImage(${index}, this)" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                                ×
                            </button>
                        `;
                        preview.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                }
            });
        } else {
            preview.classList.add('hidden');
        }
    }

    function removeNewImage(index, button) {
        const input = document.querySelector('input[name="images[]"]');
        const dt = new DataTransfer();
        
        Array.from(input.files).forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        input.files = dt.files;
        button.parentElement.remove();
        
        if (input.files.length === 0) {
            document.getElementById('image-preview').classList.add('hidden');
        }
    }
    </script>

    <!-- Image Modal -->
    @include('components.image-modal')
</x-unilink-layout>

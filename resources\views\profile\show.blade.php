<x-unilink-layout>
    <!-- Success Messages -->
    @if (session('status') === 'profile-updated')
        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                Profile updated successfully!
            </div>
        </div>
    @endif

    @if (session('status') === 'password-updated')
        <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                Password updated successfully!
            </div>
        </div>
    @endif

    <!-- Profile Header Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
        <!-- Cover Photo -->
        <div class="h-64 profile-cover relative">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
            <!-- Cover photo upload button (if own profile) -->
            @if(auth()->id() === $user->id)
                <button class="absolute bottom-4 right-4 bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-all">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Edit Cover Photo
                </button>
            @endif
        </div>

        <!-- Profile Info -->
        <div class="px-6 pb-6">
            <div class="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                <!-- Profile Picture -->
                <div class="relative -mt-16 mb-4 sm:mb-0">
                    <img class="w-32 h-32 rounded-full profile-avatar-border"
                         src="{{ $user->avatar ? asset('storage/' . $user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=7BC74D&background=EEEEEE&size=128' }}"
                         alt="{{ $user->name }}">
                    @if(auth()->id() === $user->id)
                        <button class="absolute bottom-2 right-2 bg-gray-100 hover:bg-gray-200 rounded-full p-2 shadow-md transition-colors">
                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            </svg>
                        </button>
                    @endif
                </div>

                <!-- User Info -->
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-gray-900">{{ $user->name }}</h1>
                    @if($user->bio)
                        <p class="text-gray-600 mt-1">{{ $user->bio }}</p>
                    @endif
                    <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                        @if($user->student_id)
                            <span>Student ID: {{ $user->student_id }}</span>
                        @endif
                        <span>{{ ucfirst($user->role) }}</span>
                        <span>Joined {{ $user->created_at->format('M Y') }}</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-3 mt-4 sm:mt-0">
                    @if(auth()->id() !== $user->id)
                        <button class="bg-custom-green text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-colors font-medium">
                            Message
                        </button>
                        <button class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors font-medium">
                            Follow
                        </button>
                    @endif
                </div>
            </div>

            <!-- Stats -->
            <div class="grid grid-cols-2 sm:grid-cols-5 gap-4 mt-6 pt-6 border-t border-gray-200">
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['posts_count'] }}</div>
                    <div class="text-sm text-gray-500">Posts</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['organizations_count'] }}</div>
                    <div class="text-sm text-gray-500">Organizations</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['created_organizations_count'] }}</div>
                    <div class="text-sm text-gray-500">Created</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['total_likes'] }}</div>
                    <div class="text-sm text-gray-500">Likes</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['total_comments'] }}</div>
                    <div class="text-sm text-gray-500">Comments</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - About & Profile Management -->
        <div class="lg:col-span-1 space-y-6">
            <!-- About Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">About</h2>
                <div class="space-y-3">
                    @if($user->email)
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <span class="text-gray-700">{{ $user->email }}</span>
                        </div>
                    @endif
                    @if($user->phone)
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <span class="text-gray-700">{{ $user->phone }}</span>
                        </div>
                    @endif
                    <div class="flex items-center space-x-3">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L14 7" />
                        </svg>
                        <span class="text-gray-700">{{ ucfirst($user->role) }}</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L14 7" />
                        </svg>
                        <span class="text-gray-700">Joined {{ $user->created_at->format('F Y') }}</span>
                    </div>
                </div>
            </div>

            <!-- Profile Management (Only for own profile) -->
            @if(auth()->id() === $user->id)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden" x-data="{ activeTab: 'profile' }">
                    <!-- Tab Navigation -->
                    <div class="border-b border-gray-200">
                        <div class="flex justify-between items-center px-6 py-4">
                            <nav class="flex space-x-8" aria-label="Tabs">
                                <button @click="activeTab = 'profile'" :class="activeTab === 'profile' ? 'border-custom-green text-custom-green' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors">
                                    Profile Info
                                </button>
                                <button @click="activeTab = 'password'" :class="activeTab === 'password' ? 'border-custom-green text-custom-green' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors">
                                    Password
                                </button>
                                <button @click="activeTab = 'danger'" :class="activeTab === 'danger' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors">
                                    Danger Zone
                                </button>
                            </nav>
                            <a href="{{ route('profile.edit') }}" class="text-sm text-custom-green hover:text-custom-dark-gray font-medium">
                                Full Edit Page →
                            </a>
                        </div>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <!-- Profile Information Tab -->
                        <div x-show="activeTab === 'profile'" x-transition>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Update Profile Information</h3>
                            @include('profile.partials.update-profile-information-form')
                        </div>

                        <!-- Password Tab -->
                        <div x-show="activeTab === 'password'" x-transition>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Update Password</h3>
                            @include('profile.partials.update-password-form')
                        </div>

                        <!-- Danger Zone Tab -->
                        <div x-show="activeTab === 'danger'" x-transition>
                            <h3 class="text-lg font-semibold text-red-600 mb-4">Delete Account</h3>
                            @include('profile.partials.delete-user-form')
                        </div>
                    </div>
                </div>
            @endif

            <!-- Organizations Section -->
            @if($user->activeOrganizations->count() > 0)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-gray-900">Organizations</h2>
                        <span class="text-sm text-gray-500">{{ $user->activeOrganizations->count() }}</span>
                    </div>
                    <div class="space-y-3">
                        @foreach($user->activeOrganizations->take(5) as $organization)
                            <div class="flex items-center space-x-3">
                                <img class="w-10 h-10 rounded-lg"
                                     src="{{ $organization->logo ? asset('storage/' . $organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($organization->name) . '&color=3B82F6&background=DBEAFE' }}"
                                     alt="{{ $organization->name }}">
                                <div class="flex-1 min-w-0">
                                    <a href="{{ route('organizations.show', $organization) }}" class="font-medium text-gray-900 hover:text-custom-green truncate block">
                                        {{ $organization->name }}
                                    </a>
                                    <p class="text-sm text-gray-500">{{ ucfirst($organization->pivot->role) }}</p>
                                </div>
                            </div>
                        @endforeach
                        @if($user->activeOrganizations->count() > 5)
                            <a href="{{ route('organizations.my') }}" class="text-custom-green hover:text-custom-dark-gray text-sm font-medium">
                                View all organizations
                            </a>
                        @endif
                    </div>
                </div>
            @endif
        </div>

        <!-- Right Column - Posts -->
        <div class="lg:col-span-2">
            <!-- Create Post (if own profile) -->
            @if(auth()->id() === $user->id)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                    <div class="flex items-center space-x-3">
                        <img class="h-10 w-10 rounded-full" src="{{ auth()->user()->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
                        <div class="flex-1">
                            <button onclick="openCreatePostModal()" class="w-full text-left px-4 py-3 bg-gray-100 hover:bg-gray-200 rounded-full text-gray-600 transition-colors">
                                What's on your mind, {{ auth()->user()->name }}?
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Posts Section -->
            <div class="space-y-6">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-gray-900">
                        {{ auth()->id() === $user->id ? 'Your Posts' : $user->name . "'s Posts" }}
                    </h2>
                    <span class="text-sm text-gray-500">{{ $stats['posts_count'] }} posts</span>
                </div>

                @forelse($user->posts as $post)
                    @include('components.post-card', ['post' => $post])
                @empty
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
                        <p class="text-gray-500">
                            {{ auth()->id() === $user->id ? "You haven't created any posts yet." : $user->name . " hasn't shared any posts yet." }}
                        </p>
                        @if(auth()->id() === $user->id)
                            <button onclick="openCreatePostModal()" class="mt-4 bg-custom-green text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-colors">
                                Create Your First Post
                            </button>
                        @endif
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Include Create Post Modal if it's the user's own profile -->
    @if(auth()->id() === $user->id)
        @include('components.create-post-modal')
    @endif

    <style>
        /* Profile page specific styles */
        .profile-stats {
            background: linear-gradient(135deg, #7BC74D 0%, #393E46 100%);
        }

        .profile-cover {
            background: linear-gradient(135deg, #7BC74D 0%, #393E46 100%);
        }

        .profile-avatar-border {
            box-shadow: 0 0 0 4px white, 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .hover-lift {
            transition: transform 0.2s ease-in-out;
        }

        .hover-lift:hover {
            transform: translateY(-2px);
        }
    </style>
</x-unilink-layout>

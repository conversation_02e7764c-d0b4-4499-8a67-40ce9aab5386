<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Organization extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo',
        'cover_image',
        'email',
        'phone',
        'website',
        'social_links',
        'status',
        'created_by',
    ];

    protected $casts = [
        'social_links' => 'array',
    ];

    /**
     * Get the user who created this organization
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all members of this organization
     */
    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'organization_members')
            ->withPivot(['role', 'status', 'joined_at'])
            ->withTimestamps();
    }

    /**
     * Get active members only
     */
    public function activeMembers(): BelongsToMany
    {
        return $this->members()->wherePivot('status', 'active');
    }

    /**
     * Get officers of this organization
     */
    public function officers(): BelongsToMany
    {
        return $this->members()->whereIn('organization_members.role', ['officer', 'president']);
    }

    /**
     * Get posts by this organization
     */
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }
}

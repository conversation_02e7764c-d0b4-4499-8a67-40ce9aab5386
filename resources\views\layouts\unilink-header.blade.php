<nav class="bg-white shadow-md border-b border-custom-second-darkest border-opacity-20 fixed w-full top-0 z-50">
    <div class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Left side - Logo and main nav -->
            <div class="flex items-center">
                <!-- Mobile menu button -->
                <button type="button" class="lg:hidden inline-flex items-center justify-center p-2 rounded-md text-custom-second-darkest hover:text-custom-darkest hover:bg-custom-lightest focus:outline-none focus:ring-2 focus:ring-inset focus:ring-custom-green" x-data x-on:click="$dispatch('toggle-sidebar')">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>

                <!-- Logo -->
                <div class="flex-shrink-0 flex items-center ml-4 lg:ml-0">
                    <a href="{{ route('dashboard') }}" class="text-2xl font-bold">
                        <span class="text-custom-green">Uni</span><span class="text-custom-darkest">Link</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:ml-10 lg:flex lg:space-x-8">
                    <a href="{{ route('dashboard') }}" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('dashboard') ? 'text-custom-green bg-custom-lightest' : '' }}">
                        Home
                    </a>
                    <a href="{{ route('announcements') }}" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('announcements*') ? 'text-custom-green bg-custom-lightest' : '' }}">
                        Campus Announcements
                    </a>
                    <a href="{{ route('organizations.index') }}" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('organizations*') ? 'text-custom-green bg-custom-lightest' : '' }}">
                        My Organizations
                    </a>
                    <a href="{{ route('scholarships.index') }}" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('scholarships*') ? 'text-custom-green bg-custom-lightest' : '' }}">
                        Find Scholarships
                    </a>
                </div>
            </div>

            <!-- Center - Search -->
            <div class="flex-1 max-w-2xl mx-4 lg:mx-8 hidden sm:block">
                <div class="relative">
                    <input type="text" placeholder="Search UniLink..." class="w-full pl-10 pr-4 py-2 bg-custom-lightest border-0 rounded-full focus:ring-2 focus:ring-custom-green focus:bg-white focus:shadow-md transition-all">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-custom-second-darkest" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Mobile Search Button -->
            <div class="sm:hidden">
                <button class="p-2 text-custom-second-darkest hover:text-custom-darkest focus:outline-none focus:ring-2 focus:ring-custom-green rounded-full">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
            </div>

            <!-- Right side - notifications, profile -->
            <div class="flex items-center space-x-4">

                <!-- Notifications -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="relative p-2 text-custom-second-darkest hover:text-custom-darkest focus:outline-none focus:ring-2 focus:ring-custom-green focus:ring-offset-2 rounded-full">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v6m0 0v6m0-6h6m-6 0H11" />
                        </svg>
                        <!-- Notification badge -->
                        <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-custom-green ring-2 ring-white"></span>
                    </button>

                    <!-- Notification dropdown -->
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50 border border-custom-second-darkest border-opacity-20">
                        <div class="px-4 py-2 text-sm font-medium text-custom-darkest border-b border-custom-second-darkest border-opacity-20">Notifications</div>
                        <div class="max-h-64 overflow-y-auto">
                            <!-- Notification items will be loaded here -->
                            <div class="px-4 py-3 text-sm text-custom-second-darkest">No new notifications</div>
                        </div>
                    </div>
                </div>

                <!-- Profile dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-custom-green focus:ring-offset-2">
                        <img class="h-8 w-8 rounded-full" src="{{ auth()->user()->avatar ? asset('storage/' . auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
                    </button>

                    <!-- Profile dropdown menu -->
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-custom-second-darkest border-opacity-20">
                        <div class="px-4 py-2 text-sm text-custom-darkest border-b border-custom-second-darkest border-opacity-20">
                            <div class="font-medium">{{ auth()->user()->name }}</div>
                            <div class="text-custom-second-darkest">{{ auth()->user()->email }}</div>
                        </div>
                        <a href="{{ route('profile.show') }}" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest">Profile</a>
                        @if(auth()->user()->hasManagementAccess())
                            <a href="{{ route('admin.dashboard') }}" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest">Admin Panel</a>
                        @endif
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>


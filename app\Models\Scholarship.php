<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Scholarship extends Model
{
    protected $fillable = [
        'title',
        'description',
        'amount',
        'provider',
        'requirements',
        'application_process',
        'application_url',
        'deadline',
        'eligibility_criteria',
        'required_documents',
        'status',
        'created_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'deadline' => 'date',
        'eligibility_criteria' => 'array',
        'required_documents' => 'array',
    ];

    /**
     * Get the user who created this scholarship
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all comments for this scholarship
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Get all likes for this scholarship
     */
    public function likes(): MorphMany
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    /**
     * Scope for active scholarships
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for scholarships that haven't expired
     */
    public function scopeNotExpired($query)
    {
        return $query->where('deadline', '>=', now());
    }
}
